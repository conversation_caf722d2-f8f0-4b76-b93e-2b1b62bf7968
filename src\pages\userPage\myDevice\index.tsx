import React, { useEffect, useState, useRef,useCallback } from 'react'
import Taro from '@tarojs/taro'
import { navigateTo } from '@tarojs/taro'
import { View } from '@tarojs/components'
import {
  Icon,Uploader,TextArea,Button,Input
} from "@nutui/nutui-react-taro";
import './index.scss'
import {saveFeedbackLog } from '../../../api/index'

function myDevicePage() {
  const [RenderData, setRenderData] = useState<any>({
    title:'',
    reason:'',
    desc:[]
  })//提示
  const [visible4, setVisible4] = useState(true)//提示
  const [visible1, setVisible1] = useState("")//提示
  const uploadRef = useRef(null);
  const [StringImageList, setStringImageList] = useState("")//提示
  const [contactInformation, setContactInformation] = useState("")//提示
  const uploadUrl = 'https://wx.sloctopus.com/blade-base/customInfo/uploadFile'
  const  textGroundData = [
    {
      title:'为什么第一次接保护板电池总电压与保护板输出电压不一致？',
      reason:'可能原因：',
      desc:[
        '1.	排线顺序接错，请检查排线，确定每一串电池的电压是否正确。',
        '2.	电池处于上电保护状态，请确保单节电池电压在3.0V-3.65V范围内，再连接保护板。',
        '3.	放电MOS管关闭，可能与电池容量或内阻有关，请先将偏高或偏低的电池单独放电或充电至与其他电池电压一致，再进行充放电测试。'
      ]
    },
    {
      title:'使用过程中突然断电怎么办？',
      reason:'可能原因及解决方案：',
      desc:[
        '1.	单串电池电压偏低，导致保护板触发保护。请检查单串电池电压，如有偏低，请进行充电或更换电池。',
        '2.	保护板电流设置过低或电池容量过低。请检查保护板电流设置，确保符合实际需求，并检查电池容量是否充足。',
      ]
    },
    {
      title:'保护板为什么不能放电？',
      reason:'可能原因及解决方案：',
      desc:[
        '1.	保护板与电池不匹配，请充电激活保护板或调整保护板的过流保护设置。',
        '2.	保护板接线错误或损坏，请检查保护板接线是否正确，如有损坏，请更换新的保护板。',
        '3.	开关按压时间低于0.5S，判断为误触发，未开机，重新按压开关并时间大于0.5S。',
        '4.	单节电池电压低于3.0V，长按按键开关5S或者用手机小程序启动强启功能，3分钟内可启动车辆。'
      ]
    },
    {
      title:'如何避免保护板误触发保护？',
      reason:'为避免保护板误触发保护，请确保：',
      desc:[
        '1.	保护板参数设置正确，包括过充、过放、过流、短路等保护参数。',
        '2.	保护板元件无损坏或老化，如有异常，请及时更换。',
        '3.	电池组电压、电流、温度等参数在正常范围内，避免异常波动导致保护板误判。'
      ]
    },
    {
      title:'如何检查保护板的工作状态？',
      reason:'为保护电池组和确保行车安全，请定期检查保护板的工作状态，包括：',
      desc:[
        '1.	检查保护板元件是否完好，无损坏或老化现象。',
        '2.	检查保护板接线是否牢固，无松动或脱落现象。',
        '3.	使用专业设备检测保护板的电压、电流、温度等参数，确保在正常范围内。',
        '4.	如发现保护板工作异常，请及时更换或维修。'
      ]
    },
    {
      title:'小程序为什么搜不到设备编号？',
      reason:'可能原因及解决方案：',
      desc:[
        '1.	手机蓝牙未打开，请打开手机蓝牙。',
        '2.	电池包未开机，请按开关进行开机。',
      ]
    },
     {
      title:'如何使用强启开关？',
      reason:'可能原因及解决方案：',
      desc:[
        '1.	设备连上小程序,点击左下方闪电按钮,此时缓冲成功之后显示操作成功,然后闪电图标哪里会出现30S倒计时,倒计时结束，强启成功，正常点火操作即可。',
      ]
    },
     {
      title:'冬季出车前如何操作？',
      reason:'可能原因及解决方案：',
      desc:[
        '1.打开小程序,点击一键强启闪电符号,进行强启,设备会自动运行到最佳点火状态,可保持15-30分钟,可远程在家操作。'
      ]
    },
     {
      title:'如何进行关机和什么情况下建议设备关机？',
      reason:'可能原因及解决方案：',
      desc:[
        `1.	连续3-5天不用的情况下,请向右滑动小程序右下方的关机按钮,
        关闭设备;向右滑动开关之后会出现二次滑动按钮，再次向右滑动即可关闭设备;
        设备关机之后下次使用时需要打开设备盖子，按下按键开关即可开机使用;关机后满电的情况
        下一年不用都没有问题。`,
      ]
    },
  ] 
  const routerParams = () => {
    let value = Taro.getCurrentInstance().router?.params || {};
    if(value.feedback){
      setVisible4(false);
    }
    if(value.value == '1'){
      setRenderData(textGroundData[0])
    }
    if(value.value == '2'){
      setRenderData(textGroundData[1])
    }
    if(value.value == '3'){
      setRenderData(textGroundData[2])
    }
    if(value.value == '4'){
      setRenderData(textGroundData[3])
    }
    if(value.value == '5'){
      setRenderData(textGroundData[4])
    }
    if(value.value == '6'){
      setRenderData(textGroundData[5])
    }
    if(value.value == '7'){
      setRenderData(textGroundData[6])
    }
    if(value.value == '8'){
      setRenderData(textGroundData[7])
    }
    if(value.value == '9'){
      setRenderData(textGroundData[8])
    }
  }

  const reBack = () => {
    Taro.navigateBack();
    setVisible4(true);
  }
  
  const textChange = (value) => {
    console.log(value);
    setVisible1(value);
  }

  //onStart  onSuccess onFailure onChange
  const onStart = (value) => { 
    console.log('开始了？',value);
  }
  const onSuccess = (value) => { 
    console.log('成功了？',value)
    //value.responseText.data 转化为对象
    const ImgUrl = JSON.parse(value.responseText.data)
    // ImgUrl.data.link
    console.log( ImgUrl.link,"图片地址？");
    setStringImageList(StringImageList + ImgUrl.link+',');
  }
  const onFailure = (value) => { 
    console.log('失败了？',value)
  }
  const onChange = (value) => { 
    console.log('改变了？',value)
  }

  //提交新增
  const Submit = (id = {}) => {
    const a:any = StringImageList
    let str:any = a.replace(/,$/, '');
    const obj = {
      content:visible1,
      link:str,
      phone:contactInformation==""?Taro.getStorageSync('phone'):contactInformation
    }
    saveFeedbackLog(obj).then(res => {
      Taro.showToast({
        title: '成功提交',
        icon: 'success',
        duration: 2000
      })
      setTimeout(() => {
        uploadRef.current.clear();
        setVisible1('')
        reBack();
      },2000)
    })

  }
  const skipfeedback = () => {
    Taro.navigateTo(
      {
        url: `/pages/userPage/myDevice/index?feedback=${true}`
      }
    )
  }
  useEffect(() => {
    routerParams();
  },[])


  return (
    <View className="myDevice-page">
      {/* 详情页 */}
      {visible4 &&<View>

        {/* <View className='reBack' onClick={() => reBack()}>
          <Icon name="left"></Icon>
          <View >返回</View>
        </View> */}

        <View className='container'>
          <View className='Title'>{RenderData.title}</View>
          <View className='content'>
            <View className = "contentReason">{RenderData.reason}</View>
            <View className='contentList'>
              {
                RenderData.desc.map(item => {
                  return(
                    <View className='contentList-item'>{item}</View>
                  )
                })
              
              }
            </View>
          </View>
        </View>
        <View className='buttonGroup'>
          <Button type="primary" size = "large" onClick={() => skipfeedback()} color ="#4CBBB1">问题反馈</Button>
        </View>
      </View>}
      {/* 问题反馈 */}
      {!visible4 && <View>

        <View className='TextImageInfo'>

          <View className='Title'>问题反馈</View>

          <View className='QuestionContent'>
            <View className='TextAreaContent'>
              {/* <View className='Title'>问题反馈</View> */}
              <TextArea  defaultValue={visible1}  onChange ={(value) => textChange(value)}  placeholder= "请描述你的问题" limitshow maxlength="200" />
            </View>
            <View className='hint'>添加视频或者图片</View>
            <View className='hint1'>视频不超过1分钟</View>
            <View className='UploaderContent'>
              <View className='UploaderStyle' >
                  <Uploader 
                  ref={uploadRef}
                  url={uploadUrl} 
                  multiple={true}
                  maximum="4"  
                  maximize={1024 * 1024 * 10}
                  onStart ={(value) => onStart(value)}
                  onSuccess = {(value) => onSuccess(value)}
                  onFailure = {(value) => onFailure(value)}
                  onChange = {(value) => onChange(value)}
                  />
              </View>
            </View>
          </View>
          <View className='inputContent'>
              <Input 
              name="text" 
              label="联系方式"  
              placeholder="为更好解决问题，请留下联系方式" 
              onChange={(value) => setContactInformation(value) }
              />
          </View>

          <Button size="large" type="primary" onClick={() => Submit()} color ="#4CBBB1">提交</Button>
        </View>

      </View>}

    </View>
  )
}

export default myDevicePage
