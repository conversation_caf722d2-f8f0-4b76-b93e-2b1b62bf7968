import { observable } from 'mobx'

const  bluetoothLink = observable({
    state: {
        bluetoothLink: false,
        connectionListenerSet:false,
        BlueReconnectionStatus:false,//蓝牙尝试重连状态
        ReconnectionCount:0,//重连计数
        WebSocketUpDateCount:0
    },
    setState(props) {
        this.state = {
            ...this.state,
            ...props
        }
    }
})

export default  bluetoothLink