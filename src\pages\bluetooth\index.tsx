import React, { useEffect, useRef, useState } from 'react'
import Taro, { removeStorageSync } from '@tarojs/taro'
import { View, Image } from '@tarojs/components'
import {
  Icon,
  Input,
  Animate,
  Button
} from "@nutui/nutui-react-taro";
import { inject, observer } from 'mobx-react';
import BlueImg from '@/assets/images/ikon_shebei.png'
import IconShebei from '@/assets/images/icon_shebei.png'
import bluetoothService from '@/services/bluetoothService'
import './index.scss'

const BluetoothPage = observer(({ store }) => {
  const [dataSource, setDataSource] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [serach, setSearch] = useState('')
 
  useEffect(() => {
    Taro.hideHomeButton();
    handleBegin();
    Taro.setStorageSync('noBlueToothAgain',true);
  }, [])


  const handleBegin = async() => {
    const initResult =  await bluetoothService.initBluetoothAdapter()//初始化蓝牙
    if(initResult){bluetoothService.startDiscovery()}//开始搜索设备
    else{setLoading(false);}
    setLoading(true);
    let Timer = setTimeout(() => {
      const devicesListArr = bluetoothService.getFilteredDevices()//拿取设备
      setDataSource(devicesListArr)//设置设备数据
      clearTimeout(Timer);
    },2000)
  }
  
  const createBLEConnection = async (items) => {
    const Result = await bluetoothService.connectDevice(items);//设备手动连接
    if(Result){
      setLoading(false);
      const { IsCustom } = Taro.getCurrentInstance().router?.params || {};
      if(IsCustom == "true"){
        Taro.setStorageSync('Open4GDio',true);
        Taro.setStorageSync('BluetoothEqu',items.localName);
      }
      Taro.switchTab({
        url: '/pages/home/<USER>',
      })
    }
  }
  

  const getBluetoothDevices = () => {
    const devicesListArr = bluetoothService.getFilteredDevices()
    setDataSource(devicesListArr)
  }

  let dataSourceFilter = dataSource

  if (serach) {
    dataSourceFilter = dataSource.filter(item => (item.localName || '').includes(serach) || (item.name || '').includes(serach))
  }

  const handleCancel = () => {
    handleBegin();
    Taro.switchTab({
      url: '/pages/home/<USER>',
    })
  }

  const handleBlurInput = (values) => {
    setSearch(values)
  }

  return (
    <View className="bluetooth-page">
      <View className='bluetooth-page-search'>
        <View className='bluetooth-page-search-block'>
          <Image className='bluetooth-page-search-block-img' src={BlueImg} />
          <View className="bluetooth-page-search-block-border"></View>
          {loading && <View className="scanning"></View>}
        </View>
        <View className='bluetooth-page-search-text'>
          {
            loading
              ?
              <Animate type="breath" loop>正在搜索设备，请稍后…</Animate>
              :
              '搜索已暂停'
          }
        </View>
      </View>
      <View className='bluetooth-page-title'>
        <View>扫描可用设备 </View>
        {/* <Image onClick={handleBegin} src={IconShuaxin} className='bluetooth-page-title-img' /> */}
        <View className="bluetooth-page-btn">
          <Button onClick={handleCancel} block type="warning">返 回</Button>
          <Button onClick={getBluetoothDevices} block type="primary">刷 新</Button>
        </View>
      </View>
      <View className='bluetooth-page-SearchBar'>
        <Icon name="search"></Icon>
        <Input onBlur={handleBlurInput} placeholder='请输入关键字搜索设备' />
      </View>
      <View className='bluetooth-page-body-list'>
        {
          dataSourceFilter.map(items => {
            return (
              <View
                className='bluetooth-page-body-list-li'
                key={items.deviceId}
              >
                <View className='bluetooth-page-body-list-li-row'>
                  <Image className='bluetooth-page-body-list-li-img' src={IconShebei} />
                  <View className='bluetooth-page-body-list-li-text'> {items.localName || items.name}</View>
                </View>
                <View
                  className='bluetooth-page-body-list-li-button'
                  onClick={() => createBLEConnection(items)}>
                  连接
                </View>
              </View>
            )
          })
        }

      </View>
    </View >
  )
})

export default inject(store => store)(BluetoothPage);
